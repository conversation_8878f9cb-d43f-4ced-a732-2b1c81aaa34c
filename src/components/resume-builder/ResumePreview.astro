---
// This component will display a live preview of the resume.
---

<div class="bg-transparent focus:outline-none">
    <!-- Action buttons container - positioned above the preview -->
    <div class="mb-4 text-center">
        <div id="preview-buttons" class="flex flex-wrap justify-center gap-2 bg-white/80 dark:bg-gray-800/60 backdrop-blur rounded-xl p-2 mx-auto" style="max-width: fit-content;">
            <button id="sample-data-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-green-50 text-green-700 dark:bg-green-900/40 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Sample</span>
            </button>
            <button id="clear-data-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-red-50 text-red-700 dark:bg-red-900/40 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span>Clear</span>
            </button>
            <button id="download-pdf-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                <span>Download</span>
            </button>
            <button id="toggle-preview-mode-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-orange-50 text-orange-700 dark:bg-orange-900/40 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span>Preview</span>
            </button>
            <div id="customize-toggle-buttons">
                <button id="customize-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-purple-50 text-purple-700 dark:bg-purple-900/40 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-800/50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>Customize</span>
                </button>
                <button id="back-to-edit-btn" class="hidden items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-gray-50 text-gray-700 dark:bg-gray-700/40 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800/50">
                  <span>Back to Edit</span>
                </button>
            </div>
        </div>
        <!-- Mobile zoom control -->
        <div class="flex items-center justify-center gap-2 md:hidden mt-3">
            <label for="preview-zoom" class="text-xs text-gray-500 dark:text-gray-400">Zoom</label>
            <input id="preview-zoom" type="range" min="70" max="120" value="100" class="w-40" />
        </div>
    </div>

    <div id="resume-preview-content" class="prose prose-sm dark:prose-invert max-w-full mx-auto edit-mode" data-template="harvard">
        <!-- The formatted resume content will be rendered here -->
    </div>
</div>

    <style>
        #resume-preview-content {
            /* Base A4 sheet: 210mm × 297mm rendered at ~96dpi (approx 794×1123) */
            aspect-ratio: 210 / 297;
            width: 794px; /* Actual-size width; fit-to-view scales this down when needed */
            max-height: unset;
            margin-left: auto;
            margin-right: auto;
            background: #fff;
            border: 1px solid #e5e7eb;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            padding-top: 1rem; /* Add padding at the top for the buttons */
        }

        /* Style for the action buttons container */
        #preview-buttons {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .dark #preview-buttons {
            background: rgba(31, 41, 55, 0.95);
            border-color: #374151;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }
        .two-column .main-content {
            grid-column: 2 / 3;
        }
        .two-column .sidebar {
            grid-column: 1 / 2;
        }

        /* Hide editing controls when not in edit mode */
        #resume-preview-content:not(.edit-mode) .edit-controls,
        #resume-preview-content:not(.edit-mode) [data-action],
        #resume-preview-content:not(.edit-mode) .drag-handle,
        #resume-preview-content:not(.edit-mode) .draggable[draggable="true"]::after {
            display: none !important;
        }

        /* Remove drag styling when not in edit mode */
        #resume-preview-content:not(.edit-mode) .draggable {
            cursor: default !important;
        }

        #resume-preview-content:not(.edit-mode) .draggable[draggable="true"] {
            draggable: false;
        }

        /* Disable contenteditable in preview mode */
        #resume-preview-content:not(.edit-mode) [contenteditable] {
            -webkit-user-modify: read-only;
            -moz-user-modify: read-only;
            user-modify: read-only;
            cursor: default !important;
        }

        #resume-preview-content:not(.edit-mode) [contenteditable]:focus {
            outline: none !important;
        }
    </style>

    <script>
        import { resumeData, moveSection, clearResumeData, loadSampleData } from '../../lib/resumeBuilderService';

        // Ambient type declarations for CDN libraries
        declare global {
            interface Window {
                jspdf: any;
                html2canvas: any;
            }
        }

        // Lazy loader for external PDF libs
        function loadPdfLibs(): Promise<void> {
            const ensure = (src: string, test: () => boolean) => new Promise<void>((resolve, reject) => {
                try { if (test()) return resolve(); } catch (e) {}
                const s = document.createElement('script');
                s.src = src;
                s.async = true;
                s.onload = () => resolve();
                s.onerror = () => reject(new Error('Failed to load ' + src));
                document.head.appendChild(s);
            });
            return Promise.all([
                ensure('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', () => typeof window.jspdf !== 'undefined'),
                ensure('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js', () => typeof window.html2canvas !== 'undefined')
            ]).then(() => void 0);
        }

        document.addEventListener('DOMContentLoaded', () => {
            const previewContent = document.getElementById('resume-preview-content');
            const previewWrapper = document.getElementById('resume-preview-content-wrapper');
            const downloadBtn = document.getElementById('download-pdf-btn') as HTMLButtonElement;
            const clearBtn = document.getElementById('clear-data-btn');
            const sampleBtn = document.getElementById('sample-data-btn');
            const togglePreviewBtn = document.getElementById('toggle-preview-mode-btn') as HTMLButtonElement;
            if (!previewContent || !downloadBtn || !clearBtn || !sampleBtn || !togglePreviewBtn) return;

            let isInlineEditing = false; // prevent full rerender during keystrokes

            // Function to toggle between edit and preview modes
            const setPreviewMode = (isPreviewMode: boolean) => {
                if (isPreviewMode) {
                    previewContent?.classList.remove('edit-mode');
                } else {
                    previewContent?.classList.add('edit-mode');
                }
            };

            const renderPreview = (data) => {
                // Apply dynamic styles
                const styleTag = document.getElementById('resume-dynamic-styles') || document.createElement('style');
                styleTag.id = 'resume-dynamic-styles';
                styleTag.innerHTML = `
                    #resume-preview-content {
                        font-family: ${data.fontFamily};
                        font-size: ${data.fontSize};
                        line-height: ${data.lineSpacing};
                        color: ${data.bodyTextColor};
                    }
                    #resume-preview-content.harvard h2 {
                        letter-spacing: 0.02em;
                    }
                    #resume-preview-content h1 {
                        font-size: ${data.headingStyles.h1FontSize};
                        font-weight: ${data.headingStyles.h1FontWeight};
                    }
                    #resume-preview-content h2 {
                        font-size: ${data.headingStyles.h2FontSize};
                        font-weight: ${data.headingStyles.h2FontWeight};
                    }
                    #resume-preview-content h1,
                    #resume-preview-content h2,
                    #resume-preview-content h3 {
                        color: ${data.headingColor || data.bodyTextColor};
                        text-transform: ${data.headingStyles.allCaps ? 'uppercase' : 'none'};
                        text-decoration: ${data.headingStyles.underlined ? 'underline' : 'none'};
                        border-bottom: ${data.headingStyles.horizontalRule ? `1px solid ${data.bodyTextColor}` : 'none'};
                        padding-bottom: ${data.headingStyles.horizontalRule ? '0.25rem' : '0'};
                    }
                    #resume-preview-content .section-margin {
                        margin-bottom: ${data.sectionSpacing === 'compact' ? '0.5rem' : data.sectionSpacing === 'relaxed' ? '1.5rem' : '1rem'};
                    }
                    #resume-preview-content ul {
                        list-style-position: outside; /* bullets outside so user credentials line doesn't show bullets */
                        padding-left: 1.25rem;
                        margin: 0.25rem 0 0.25rem 0;
                    }
                    #resume-preview-content .heading-bar {
                        text-transform: uppercase;
                        font-weight: 700;
                        border-bottom: 1px solid ${data.bodyTextColor};
                        padding-bottom: 0.15rem;
                        margin-bottom: 0.4rem;
                        letter-spacing: 0.03em;
                    }
                    #resume-preview-content .meta-line {
                        display: flex; justify-content: space-between; align-items: baseline;
                    }
                    #resume-preview-content .company-role { font-weight: 700; }
                    #resume-preview-content .italic { font-style: italic; }
                `;
                document.head.appendChild(styleTag);

                previewContent.style.padding = data.margins === 'narrow' ? '1.25rem 1.5rem' : data.margins === 'wide' ? '3rem 3.5rem' : '2rem 2.5rem';
                previewContent.classList.remove('two-column');

                const formatDate = (dateStr) => {
                    if (!dateStr) return '';
                    const [year, month] = dateStr.split('-');
                    const date = new Date(parseInt(year), parseInt(month) - 1);
                    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: data.dateFormat === 'short' ? 'short' : 'long' };
                    return date.toLocaleDateString('en-US', options);
                };
                // Harvard layout builder (single column)
                    const contactLineParts = [
                    data.contactInfo.email,
                    data.contactInfo.phone,
                    data.contactInfo.location,
                    data.contactInfo.linkedin ? `LinkedIn: ${data.contactInfo.linkedin}` : null,
                    data.contactInfo.portfolio ? `Portfolio: ${data.contactInfo.portfolio}` : null,
                ].filter(Boolean);

                const harvard = [] as string[];
                harvard.push(`
                    <header style="text-align:center; margin-bottom:0.5rem;">
                        <h1 style="margin:0; border:none;">${data.contactInfo.fullName || ''}</h1>
                        <div class="italic" style="border:none; padding:0; margin-top:0.25rem;">
                            ${contactLineParts.join(' | ')}
                        </div>
                    </header>
                `);

                if (data.sectionVisibility.summary) {
                    harvard.push(`
                        <section class="section-margin">
                            <div class="heading-bar">Summary</div>
                            <div id="inline-summary" contenteditable="true" data-placeholder="Write a short professional summary..." style="outline:none;">${data.summary || ''}</div>
                        </section>
                    `);
                }

                if (data.sectionVisibility.workExperience) {
                    harvard.push(`<section class="section-margin" id="section-experience"><div class="heading-bar">Experience</div>`);
                    if (Array.isArray(data.workExperience) && data.workExperience.length) {
                        data.workExperience.forEach((job) => {
                            harvard.push(`
                                <div class="mt-3 draggable" data-drag-type="work" draggable="true" data-work-id="${job.id}">
                                    <div class="meta-line">
                                        <div class="company-role">${job.jobTitle || 'Job Title'}${job.company ? `, ${job.company}` : ''}</div>
                                        <div class="italic">${job.location ? job.location + ' \u00B7 ' : ''}${formatDate(job.startDate)} - ${job.endDate === 'Present' ? 'Present' : formatDate(job.endDate)}</div>
                                    </div>
                                <ul class="list-disc mt-1" data-bullets>
                                    ${(job.bulletPoints && job.bulletPoints.length > 0 ? job.bulletPoints.map(bp => `<li contenteditable=\"true\" data-bullet-id=\"${bp.id}\">${bp.text || ''}</li>`).join('') : '<li contenteditable=\"true\">Describe impact and achievements</li>')}
                                </ul>
                                <div class="edit-controls">
                                    <button class="text-xs text-indigo-600 mt-1" data-action="add-bullet">+ Add bullet</button>
                                    <div class="mt-1 flex gap-3 text-xs">
                                      <button class="text-gray-600 hover:text-indigo-600" data-action="edit-job">Edit</button>
                                      <button class="text-gray-600 hover:text-indigo-600" data-action="remove-job">Remove role</button>
                                      <span class="cursor-grab text-gray-400 drag-handle" title="Drag to reorder">⇅ Drag</span>
                                    </div>
                                </div>
                                </div>
                            `);
                        });
                    } else {
                        harvard.push(`<p class="text-gray-500 italic mt-1">Add your work experience.</p>`);
                    }
                    harvard.push(`<div class="edit-controls"><button class="text-xs text-indigo-600 mt-2" data-action="add-job">+ Add Job</button></div></section>`);
                }

                if (data.sectionVisibility.education) {
                    harvard.push(`<section class="section-margin" id="section-education"><div class="heading-bar">Education</div>`);
                    if (Array.isArray(data.education) && data.education.length) {
                        data.education.forEach((edu) => {
                            harvard.push(`
                                <div class="mt-3 draggable" data-drag-type="edu" draggable="true" data-edu-id="${edu.id}">
                                    <div class="meta-line">
                                        <div class="company-role">${edu.degree || 'Degree'}${edu.fieldOfStudy ? `, ${edu.fieldOfStudy}` : ''} — ${edu.institution || 'Institution'}</div>
                                        <div class="italic">${formatDate(edu.graduationDate)}</div>
                                    </div>
                                     ${edu.details ? `<div>${edu.details}</div>` : ''}
                                     <div class="edit-controls">
                                         <div class="mt-1 flex gap-3 text-xs">
                                           <button class="text-gray-600 hover:text-indigo-600" data-action="edit-edu">Edit</button>
                                           <button class="text-gray-600 hover:text-indigo-600" data-action="remove-edu">Remove education</button>
                                           <span class="cursor-grab text-gray-400 drag-handle" title="Drag to reorder">⇅ Drag</span>
                                        </div>
                                     </div>
                                </div>
                            `);
                        });
                    } else {
                        harvard.push(`<p class="text-gray-500 italic mt-1">Add your education history.</p>`);
                    }
                    harvard.push(`<div class="edit-controls"><button class="text-xs text-indigo-600 mt-2" data-action="add-edu">+ Add Education</button></div></section>`);
                }

                if (data.sectionVisibility.skills) {
                    harvard.push(`
                        <section class="section-margin">
                            <div class="heading-bar">Skills</div>
                            <div id="inline-skills" contenteditable="true" data-placeholder="e.g., JavaScript, React, Node.js">${data.skills && data.skills.length ? data.skills.join(', ') : ''}</div>
                        </section>
                    `);
                }

                const customVisible = data.customSections.filter(s => s.visible);
                if (customVisible.length) {
                    customVisible.forEach((section) => {
                        harvard.push(`
                            <section class="section-margin">
                                <div class="heading-bar">${section.title}</div>
                                <div class="prose">${section.content}</div>
                            </section>
                        `);
                    });
                }

                previewContent.classList.add('harvard');
                previewContent.innerHTML = harvard.join('');

                // Inline editing wiring
                const debounce = (fn, ms = 200) => { let t; return (...a) => { clearTimeout(t); t = setTimeout(() => fn(...a), ms); }; };
                    const inlineSummary = document.getElementById('inline-summary');
                    if (inlineSummary) {
                        // Prevent focus loss while typing by avoiding full rerender on every keystroke
                        inlineSummary.addEventListener('focus', () => { isInlineEditing = true; });
                        inlineSummary.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                        inlineSummary.addEventListener('input', debounce(() => {
                            import('../../lib/resumeBuilderService').then(m => m.updateSection('summary', (inlineSummary as HTMLElement).innerText.trim()));
                        }));
                    }
                const inlineSkills = document.getElementById('inline-skills');
                if (inlineSkills) {
                    inlineSkills.addEventListener('input', debounce(() => {
                        const val = (inlineSkills as HTMLElement).innerText.split(',').map(s => s.trim()).filter(Boolean);
                        import('../../lib/resumeBuilderService').then(m => m.updateSection('skills', val as any));
                    }));
                }
                // Work bullets inline
                    previewContent.querySelectorAll('[data-work-id]').forEach((jobEl) => {
                    const jobId = (jobEl as HTMLElement).dataset.workId!;
                        jobEl.querySelectorAll('li[contenteditable][data-bullet-id]').forEach(li => {
                            li.addEventListener('focus', () => { isInlineEditing = true; });
                            li.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                            li.addEventListener('input', debounce(() => {
                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();
                                    const list = curr.workExperience.map(j => j.id === jobId ? { ...j, bulletPoints: j.bulletPoints.map(bp => bp.id === (li as HTMLElement).dataset.bulletId ? { ...bp, text: (li as HTMLElement).innerText } : bp) } : j);
                                    m.updateSection('workExperience', list as any);
                                });
                            }));
                        });
                    const addBtn = jobEl.querySelector('[data-action="add-bullet"]');
                    if (addBtn) {
                        addBtn.addEventListener('click', () => {
                            import('../../lib/resumeBuilderService').then(m => {
                                const curr = m.resumeData.get();
                                const list = curr.workExperience.map(j => j.id === jobId ? { ...j, bulletPoints: [...(j.bulletPoints||[]), { id: crypto.randomUUID(), text: '' }] } : j);
                                m.updateSection('workExperience', list as any);
                            });
                        });
                    }
                    const removeBtn = jobEl.querySelector('[data-action="remove-job"]');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            if (!confirm('Remove this role?')) return;
                            import('../../lib/resumeBuilderService').then(m => m.removeItem('workExperience', jobId));
                        });
                    }
                });

                // Add Job button
                const addJobBtn = document.querySelector('#section-experience [data-action="add-job"]') as HTMLButtonElement | null;
                if (addJobBtn) {
                    addJobBtn.addEventListener('click', () => {
                        import('../../lib/resumeBuilderService').then(m => m.addItem('workExperience', { bulletPoints: [] }));
                    });
                }

                // Remove Education buttons
                previewContent.querySelectorAll('[data-edu-id]').forEach((eduEl) => {
                    const eduId = (eduEl as HTMLElement).dataset.eduId!;
                    const removeBtn = eduEl.querySelector('[data-action="remove-edu"]');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            if (!confirm('Remove this education entry?')) return;
                            import('../../lib/resumeBuilderService').then(m => m.removeItem('education', eduId));
                        });
                    }
                });

                // Add Education button
                const addEduBtn = document.querySelector('#section-education [data-action="add-edu"]') as HTMLButtonElement | null;
                if (addEduBtn) {
                    addEduBtn.addEventListener('click', () => {
                        import('../../lib/resumeBuilderService').then(m => m.addItem('education'));
                    });
                }

                // Drag to reorder (work and education)
                let dragId: string | null = null;
                let dragType: 'work' | 'edu' | null = null;
                previewContent.querySelectorAll('.draggable[draggable="true"]').forEach(el => {
                    el.addEventListener('dragstart', (e) => {
                        dragId = (el as HTMLElement).dataset.workId || (el as HTMLElement).dataset.eduId || null;
                        dragType = ((el as HTMLElement).dataset.dragType as any) || null;
                        (e as DragEvent).dataTransfer?.setData('text/plain', dragId || '');
                    });
                    el.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        (el as HTMLElement).classList.add('drag-over');
                    });
                    el.addEventListener('dragleave', () => (el as HTMLElement).classList.remove('drag-over'));
                    el.addEventListener('drop', () => {
                        (el as HTMLElement).classList.remove('drag-over');
                        const targetId = (el as HTMLElement).dataset.workId || (el as HTMLElement).dataset.eduId || null;
                        if (!dragId || !targetId || !dragType) return;
                        if (dragId === targetId) return;
                        import('../../lib/resumeBuilderService').then(m => {
                            const state = m.resumeData.get();
                            if (dragType === 'work') {
                                const list = [...state.workExperience];
                                const from = list.findIndex(i => i.id === dragId);
                                const to = list.findIndex(i => i.id === targetId);
                                if (from === -1 || to === -1) return;
                                const [moved] = list.splice(from, 1);
                                list.splice(to, 0, moved);
                                m.updateSection('workExperience', list as any);
                            } else {
                                const list = [...state.education];
                                const from = list.findIndex(i => i.id === dragId);
                                const to = list.findIndex(i => i.id === targetId);
                                if (from === -1 || to === -1) return;
                                const [moved] = list.splice(from, 1);
                                list.splice(to, 0, moved);
                                m.updateSection('education', list as any);
                            }
                        });
                    });
                });

                // Inline popovers for structured fields
                function openJobPopover(hostEl, jobId) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    pop.innerHTML = `
                      <div class="popover-card">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          <label class="block text-xs">Title<input type="text" data-field="jobTitle" class="pop-input"></label>
                          <label class="block text-xs">Company<input type="text" data-field="company" class="pop-input"></label>
                          <label class="block text-xs">Location<input type="text" data-field="location" class="pop-input"></label>
                          <label class="block text-xs">Start<input type="month" data-field="startDate" class="pop-input"></label>
                          <label class="block text-xs">End<input type="month" data-field="endDate" class="pop-input"></label>
                        </div>
                        <div class="mt-2 flex justify-end gap-2">
                          <button class="pop-close text-xs px-2 py-1">Close</button>
                        </div>
                      </div>`;
                    document.body.appendChild(pop);
                    const rect = hostEl.getBoundingClientRect();
                    pop.style.left = `${Math.round(rect.left + window.scrollX)}px`;
                    pop.style.top = `${Math.round(rect.top + window.scrollY + rect.height + 6)}px`;
                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const job = m.resumeData.get().workExperience.find(j => j.id === jobId);
                        if (!job) return;
                        (pop.querySelector('[data-field="jobTitle"]') as HTMLInputElement).value = job.jobTitle || '';
                        (pop.querySelector('[data-field="company"]') as HTMLInputElement).value = job.company || '';
                        (pop.querySelector('[data-field="location"]') as HTMLInputElement).value = job.location || '';
                        (pop.querySelector('[data-field="startDate"]') as HTMLInputElement).value = job.startDate || '';
                        (pop.querySelector('[data-field="endDate"]') as HTMLInputElement).value = typeof job.endDate === 'string' ? job.endDate : '';
                        const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                        pop.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>('.pop-input').forEach((inp)=>{
                          inp.addEventListener('input', deb(()=>{
                            const field = (inp as HTMLElement).dataset.field as string;
                            const value = (inp as HTMLInputElement).value;
                            const current = m.resumeData.get().workExperience.map((j: any) => j.id === jobId ? { ...j, [field]: value } : j);
                            m.updateSection('workExperience', current as any);
                          }));
                        });
                    });
                    pop.querySelector('.pop-close')?.addEventListener('click', () => pop.remove());
                }

                function openEduPopover(hostEl, eduId) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    pop.innerHTML = `
                      <div class="popover-card">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          <label class="block text-xs">Institution<input type="text" data-field="institution" class="pop-input"></label>
                          <label class="block text-xs">Degree<input type="text" data-field="degree" class="pop-input"></label>
                          <label class="block text-xs">Field<input type="text" data-field="fieldOfStudy" class="pop-input"></label>
                          <label class="block text-xs">Graduation<input type="month" data-field="graduationDate" class="pop-input"></label>
                        </div>
                        <div class="mt-2"><label class="block text-xs">Details<textarea data-field="details" class="pop-input" rows="3"></textarea></label></div>
                        <div class="mt-2 flex justify-end gap-2"><button class="pop-close text-xs px-2 py-1">Close</button></div>
                      </div>`;
                    document.body.appendChild(pop);
                    const rect = hostEl.getBoundingClientRect();
                    pop.style.left = `${Math.round(rect.left + window.scrollX)}px`;
                    pop.style.top = `${Math.round(rect.top + window.scrollY + rect.height + 6)}px`;
                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const edu = m.resumeData.get().education.find(e => e.id === eduId);
                        if (!edu) return;
                        (pop.querySelector('[data-field="institution"]') as HTMLInputElement).value = edu.institution || '';
                        (pop.querySelector('[data-field="degree"]') as HTMLInputElement).value = edu.degree || '';
                        (pop.querySelector('[data-field="fieldOfStudy"]') as HTMLInputElement).value = edu.fieldOfStudy || '';
                        (pop.querySelector('[data-field="graduationDate"]') as HTMLInputElement).value = edu.graduationDate || '';
                        (pop.querySelector('[data-field="details"]') as HTMLTextAreaElement).value = edu.details || '';
                        const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                        pop.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>('.pop-input').forEach((inp)=>{
                          inp.addEventListener('input', deb(()=>{
                            const field = (inp as HTMLElement).dataset.field as string;
                            const value = (inp as HTMLInputElement).value;
                            const current = m.resumeData.get().education.map((e: any) => e.id === eduId ? { ...e, [field]: value } : e);
                            m.updateSection('education', current as any);
                          }));
                        });
                    });
                    pop.querySelector('.pop-close')?.addEventListener('click', () => pop.remove());
                }

                // Wire edit buttons
                previewContent.querySelectorAll('[data-work-id] [data-action="edit-job"]').forEach((btn) => {
                    btn.addEventListener('click', () => {
                        const host = (btn as HTMLElement).closest('[data-work-id]') as HTMLElement | null;
                        if (!host) return;
                        openJobPopover(host, (host.dataset as any).workId);
                    });
                });
                previewContent.querySelectorAll('[data-edu-id] [data-action="edit-edu"]').forEach((btn) => {
                    btn.addEventListener('click', () => {
                        const host = (btn as HTMLElement).closest('[data-edu-id]') as HTMLElement | null;
                        if (!host) return;
                        openEduPopover(host, (host.dataset as any).eduId);
                    });
                });

                // Floating inline formatting toolbar (bold/italic)
                let toolbar = document.getElementById('inline-toolbar') as HTMLElement | null;
                if (!toolbar) {
                  toolbar = document.createElement('div');
                  toolbar.id = 'inline-toolbar';
                  toolbar.className = 'inline-toolbar hidden';
                  toolbar.innerHTML = `<button data-cmd="bold" title="Bold">B</button><button data-cmd="italic" title="Italic"><em>I</em></button>`;
                  document.body.appendChild(toolbar);
                  toolbar.addEventListener('mousedown', e => e.preventDefault());
                  toolbar.addEventListener('click', (e) => {
                    const btn = (e.target as HTMLElement).closest('button[data-cmd]');
                    if (!btn) return;
                    const cmd = (btn as HTMLElement).dataset.cmd as string;
                    document.execCommand(cmd, false);
                  });
                }
                function showToolbar() {
                  const sel = window.getSelection();
                  if (!sel || sel.rangeCount === 0) return hideToolbar();
                  const range = sel.getRangeAt(0);
                  if (!previewContent || !previewContent.contains(range.commonAncestorContainer)) return hideToolbar();
                  const rect = range.getBoundingClientRect();
                  if (!toolbar) return;
                  toolbar.style.left = `${rect.left + window.scrollX}px`;
                  toolbar.style.top = `${rect.top + window.scrollY - 34}px`;
                  toolbar.classList.remove('hidden');
                }
                function hideToolbar() { document.getElementById('inline-toolbar')?.classList.add('hidden'); }
                document.addEventListener('selectionchange', () => {
                  const sel = window.getSelection();
                  if (!sel || sel.isCollapsed) return hideToolbar();
                  showToolbar();
                });
            };

            // rAF batch updates
            let rafPending = false;
            let latest: any = null;
            const unsubscribe = resumeData.subscribe((data: any) => {
                console.log('ResumePreview received data update:', data);
                latest = data;
                if (rafPending || isInlineEditing) return; // don't rerender while typing
                rafPending = true;
                requestAnimationFrame(() => {
                    rafPending = false;
                    renderPreview(latest);
                });
            });

            downloadBtn.addEventListener('click', async () => {
                try {
                    downloadBtn.disabled = true;
                    const downloadButtonSpan = downloadBtn.querySelector('span');
                    if (downloadButtonSpan) downloadButtonSpan.textContent = 'Preparing...';

                    // Switch to preview mode to hide editing controls
                    setPreviewMode(true);

                    await loadPdfLibs();
                    const { jsPDF } = window.jspdf;

                    // Ensure fonts/layout are settled before capture
                    try { await (document as any).fonts?.ready; } catch {}

                    if (downloadButtonSpan) downloadButtonSpan.textContent = 'Generating...';

                    if (!previewContent) {
                        throw new Error('Preview element not found');
                    }

                    // Blur any focused editor caret and scroll to top to stabilize layout
                    try { (document.activeElement as HTMLElement | null)?.blur?.(); } catch {}
                    try { window.scrollTo({ top: 0 }); } catch {}

                    // Compute explicit capture dimensions to avoid zero-sized canvas due to transforms/content-visibility
                    const liveEl = previewContent as HTMLElement;
                    const fallbackW = 794;
                    const captureWidth = Math.max(liveEl.scrollWidth || liveEl.clientWidth || fallbackW, fallbackW);
                    const captureHeight = (liveEl.scrollHeight || liveEl.clientHeight || Math.round(captureWidth * (297 / 210)));

                    // Use reasonable scale and ensure white background to avoid transparency artifacts
                    const scale = Math.min(2, (window.devicePixelRatio || 1));
                    const canvas = await window.html2canvas(previewContent, {
                        scale,
                        useCORS: true,
                        backgroundColor: '#ffffff',
                        logging: false,
                        width: captureWidth,
                        height: captureHeight,
                        windowWidth: Math.max(document.documentElement.clientWidth, captureWidth),
                        windowHeight: Math.max(document.documentElement.clientHeight, captureHeight),
                        // Neutralize transforms and transient UI in cloned DOM before rendering
                        onclone: (clonedDoc: Document) => {
                            // Normalize page defaults to avoid extra whitespace
                            const body = clonedDoc.body as HTMLElement;
                            body.style.margin = '0';
                            body.style.padding = '0';
                            body.style.background = '#ffffff';

                            const clonedEl = clonedDoc.getElementById('resume-preview-content') as HTMLElement | null;
                            if (clonedEl) {
                                // Neutralize transforms/visuals and pin explicit size
                                clonedEl.style.transform = 'none';
                                clonedEl.style.boxShadow = 'none';
                                clonedEl.style.maxHeight = 'unset';
                                (clonedEl.style as any).aspectRatio = 'auto';
                                clonedEl.style.width = `${captureWidth}px`;
                                clonedEl.style.height = `${captureHeight}px`;
                                clonedEl.style.margin = '0';
                                clonedEl.style.left = '0';
                                clonedEl.style.top = '0';
                                clonedEl.style.position = 'static';

                                // Anchor the element at 0,0 to eliminate centering offsets
                                const wrapper = clonedDoc.createElement('div');
                                wrapper.style.position = 'fixed';
                                wrapper.style.left = '0';
                                wrapper.style.top = '0';
                                wrapper.style.width = `${captureWidth}px`;
                                wrapper.style.height = `${captureHeight}px`;
                                wrapper.style.background = '#ffffff';

                                clonedEl.parentNode?.insertBefore(wrapper, clonedEl);
                                wrapper.appendChild(clonedEl);
                            }

                            // Remove transient UI and editing controls
                            const toolbar = clonedDoc.getElementById('inline-toolbar');
                            if (toolbar && toolbar.parentNode) toolbar.parentNode.removeChild(toolbar);
                            clonedDoc.querySelectorAll('.inline-popover').forEach((el) => el.parentNode?.removeChild(el));
                            clonedDoc.querySelectorAll('.edit-controls').forEach((el) => el.parentNode?.removeChild(el));
                            clonedDoc.querySelectorAll('[data-action]').forEach((el) => el.parentNode?.removeChild(el));
                            clonedDoc.querySelectorAll('.drag-handle').forEach((el) => el.parentNode?.removeChild(el));
                        },
                        ignoreElements: (element: Element) => {
                            return element.id === 'inline-toolbar' ||
                                   element.classList.contains('inline-popover') ||
                                   element.classList.contains('edit-controls') ||
                                   element.classList.contains('drag-handle') ||
                                   element.hasAttribute('data-action');
                        }
                    } as any);

                    // Track effective bitmap size (may be updated by fallback)
                    let effWidth = canvas.width;
                    let effHeight = canvas.height;

                    // Prefer JPEG to avoid jsPDF PNG parser issues; validate and fallback strategies
                    let imgType: 'JPEG' | 'PNG' = 'JPEG';
                    console.debug('[PDF] Canvas size', { w: canvas.width, h: canvas.height, dpr: window.devicePixelRatio });
                    let imgData = canvas.toDataURL('image/jpeg', 0.98);
                    const isValidDataUrl = (data: string, prefix: string) =>
                        typeof data === 'string' && data.startsWith(prefix) && data.length > 1000;

                    // Try PNG if JPEG invalid
                    if (!isValidDataUrl(imgData, 'data:image/jpeg')) {
                        console.warn('[PDF] JPEG export invalid, retrying with PNG');
                        imgData = canvas.toDataURL('image/png');
                        imgType = 'PNG';
                    }

                    // If still invalid, attempt a second capture with fixed dimensions + blob encoding
                    if (!isValidDataUrl(imgData, imgType === 'PNG' ? 'data:image/png' : 'data:image/jpeg')) {
                        console.warn('[PDF] Primary export invalid; attempting fallback capture with fixed dimensions');
                        try {
                            const rect = (previewContent as HTMLElement).getBoundingClientRect();
                            const width = Math.max(Math.ceil(rect.width), (previewContent as HTMLElement).scrollWidth || 794);
                            const height = (previewContent as HTMLElement).scrollHeight || Math.ceil(rect.height);

                            const fallbackCanvas = await window.html2canvas(previewContent, {
                                scale: 1,
                                useCORS: true,
                                backgroundColor: '#ffffff',
                                logging: false,
                                width,
                                height,
                                windowWidth: Math.max(document.documentElement.clientWidth, width),
                                windowHeight: Math.max(document.documentElement.clientHeight, height),
                                removeContainer: true,
                                onclone: (clonedDoc: Document) => {
                                    const clonedEl = clonedDoc.getElementById('resume-preview-content') as HTMLElement | null;
                                    if (clonedEl) {
                                        clonedEl.style.transform = 'none';
                                        clonedEl.style.boxShadow = 'none';
                                    }
                                    clonedDoc.getElementById('inline-toolbar')?.remove();
                                    clonedDoc.querySelectorAll('.inline-popover').forEach(el => el.remove());
                                    clonedDoc.querySelectorAll('.edit-controls').forEach(el => el.remove());
                                    clonedDoc.querySelectorAll('[data-action]').forEach(el => el.remove());
                                    clonedDoc.querySelectorAll('.drag-handle').forEach(el => el.remove());
                                }
                            } as any);

                            // Update effective bitmap size from fallback canvas
                            effWidth = fallbackCanvas.width;
                            effHeight = fallbackCanvas.height;

                            // Re-encode via Blob -> DataURL to reduce memory pressure vs toDataURL
                            const blob: Blob = await new Promise((resolve, reject) => {
                                try {
                                    fallbackCanvas.toBlob((b) => b ? resolve(b) : reject(new Error('toBlob returned null')), 'image/jpeg', 0.95);
                                } catch (err) { reject(err as any); }
                            });

                            imgType = 'JPEG';
                            imgData = await new Promise<string>((resolve, reject) => {
                                const reader = new FileReader();
                                reader.onload = () => resolve(String(reader.result));
                                reader.onerror = () => reject(new Error('Failed to read blob as dataURL'));
                                reader.readAsDataURL(blob);
                            });

                            if (!isValidDataUrl(imgData, 'data:image/jpeg')) {
                                throw new Error('Fallback capture invalid');
                            }
                        } catch (e) {
                            console.error('[PDF] Fallback capture failed:', e);
                            throw new Error('Canvas export failed: empty or invalid data URL');
                        }
                    }

                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'pt',
                        format: 'a4'
                    });

                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();

                    // Helper: DataURL -> HTMLImageElement
                    const dataUrlToImage = (dataUrl: string) => new Promise<HTMLImageElement>((resolve, reject) => {
                        const im = new Image();
                        // Important for cross-origin safety with data URLs
                        try { im.crossOrigin = 'anonymous'; } catch {}
                        im.onload = () => resolve(im);
                        im.onerror = (e) => reject(e);
                        im.src = dataUrl;
                    });

                    // Helper: Trim white/transparent margins from a canvas
                    function trimCanvasWhitespace(src: HTMLCanvasElement): HTMLCanvasElement {
                        const w = src.width, h = src.height;
                        const ctx = src.getContext('2d');
                        if (!ctx || !w || !h) return src;
                        const img = ctx.getImageData(0, 0, w, h).data;

                        let top = h, left = w, right = 0, bottom = 0;
                        // Scan for any non-white pixel (allow near-white to be treated as white), also consider alpha
                        for (let y = 0; y < h; y++) {
                            for (let x = 0; x < w; x++) {
                                const i = (y * w + x) * 4;
                                const r = img[i], g = img[i + 1], b = img[i + 2], a = img[i + 3];
                                const isOpaque = a > 10;
                                const isWhiteish = r > 245 && g > 245 && b > 245;
                                if (isOpaque && !isWhiteish) {
                                    if (x < left) left = x;
                                    if (x > right) right = x;
                                    if (y < top) top = y;
                                    if (y > bottom) bottom = y;
                                }
                            }
                        }
                        // Nothing found; return original
                        if (right <= left || bottom <= top) return src;

                        const tw = right - left + 1;
                        const th = bottom - top + 1;

                        const out = document.createElement('canvas');
                        out.width = tw;
                        out.height = th;
                        const octx = out.getContext('2d')!;
                        octx.drawImage(src, left, top, tw, th, 0, 0, tw, th);
                        return out;
                    }

                    // Build a tightly-cropped image canvas from the captured data URL
                    const baseImg = await dataUrlToImage(imgData);
                    const srcW = baseImg.naturalWidth || effWidth || canvas.width;
                    const srcH = baseImg.naturalHeight || effHeight || canvas.height;

                    const srcCanvas = document.createElement('canvas');
                    srcCanvas.width = srcW;
                    srcCanvas.height = srcH;
                    const sctx = srcCanvas.getContext('2d')!;
                    sctx.fillStyle = '#ffffff';
                    sctx.fillRect(0, 0, srcW, srcH);
                    sctx.drawImage(baseImg, 0, 0);

                    const contentCanvas = trimCanvasWhitespace(srcCanvas);
                    const contentW = contentCanvas.width;
                    const contentH = contentCanvas.height;
                    if (!contentW || !contentH) {
                        throw new Error('Trim produced empty canvas');
                    }

                    // Calculate pixel height per PDF page to render full-bleed (no margins)
                    // scaleFactor = PDF points per pixel when fitting width to pageWidth
                    const scaleFactor = pageWidth / contentW; // pt/px
                    const pxPerPage = Math.floor(pageHeight / scaleFactor); // pixels that fill one page height

                    // Slice the content vertically into multiple pages if needed
                    const pageCanvas = document.createElement('canvas');
                    const pctx = pageCanvas.getContext('2d')!;
                    let yOffset = 0;
                    let pageIndex = 0;

                    while (yOffset < contentH) {
                        const sliceH = Math.min(pxPerPage, contentH - yOffset);

                        pageCanvas.width = contentW;
                        pageCanvas.height = sliceH;
                        pctx.clearRect(0, 0, pageCanvas.width, pageCanvas.height);
                        pctx.drawImage(
                            contentCanvas,
                            0, yOffset, contentW, sliceH,
                            0, 0, contentW, sliceH
                        );

                        // Always encode pages as JPEG for smaller size and compatibility
                        const pageDataUrl = pageCanvas.toDataURL('image/jpeg', 0.95);

                        if (pageIndex > 0) {
                            pdf.addPage();
                        }

                        const renderH = sliceH * scaleFactor; // points
                        // Top-left at 0,0; full width equals pageWidth; height matches proportional height
                        pdf.addImage(pageDataUrl, 'JPEG', 0, 0, pageWidth, renderH);

                        yOffset += sliceH;
                        pageIndex++;
                    }

                    pdf.save('resume.pdf');

                } catch (error) {
                    console.error("Error generating PDF:", error);
                    alert("Failed to generate PDF. Please try again.");
                } finally {
                    // Restore edit mode
                    setPreviewMode(false);

                    downloadBtn.disabled = false;
                    const downloadButtonSpan = downloadBtn.querySelector('span');
                    if (downloadButtonSpan) {
                        downloadButtonSpan.textContent = 'Download';
                    }
                }
            });

            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all data and start with a blank resume?')) {
                    clearResumeData();
                }
            });

            sampleBtn.addEventListener('click', () => {
                loadSampleData();
            });

            // Toggle preview mode button
            let isPreviewMode = false;
            togglePreviewBtn.addEventListener('click', () => {
                isPreviewMode = !isPreviewMode;
                setPreviewMode(isPreviewMode);
                const span = togglePreviewBtn.querySelector('span');
                if (span) {
                    span.textContent = isPreviewMode ? 'Edit' : 'Preview';
                }
            });

            window.addEventListener('beforeunload', unsubscribe);

            // Zoom control
            const zoom = document.getElementById('preview-zoom') as HTMLInputElement | null;
            if (zoom) {
                zoom.addEventListener('input', (e) => {
                    const v = Number((e.target as HTMLInputElement).value) / 100;
                    (previewContent as HTMLElement).style.transformOrigin = 'top center';
                    (previewContent as HTMLElement).style.transform = `scale(${v})`;
                });
            }

            // Fit-to-view (default) vs actual size toggle
            const toggleFitBtn = document.createElement('button');
            toggleFitBtn.id = 'toggle-fit-btn';
            toggleFitBtn.className = 'inline-flex items-center gap-1.5 text-xs font-bold px-3 py-1.5 rounded-full transition-colors bg-gray-100 text-gray-700 dark:bg-gray-700/40 dark:text-gray-200';
            toggleFitBtn.innerHTML = '<span>Actual size</span>';
            const buttons = document.getElementById('preview-buttons');
            if (buttons) buttons.appendChild(toggleFitBtn);

            let userOverride = false;
            const PREF_KEY = 'prax_resume_preview_mode';
            function fitToView() {
                if (!previewWrapper || !previewContent || userOverride) return;
                const avail = (previewWrapper as HTMLElement).clientHeight;
                const contentHeight = (previewContent as HTMLElement).scrollHeight;
                const scale = Math.min(1, avail / contentHeight);
                (previewContent as HTMLElement).style.transformOrigin = 'top center';
                (previewContent as HTMLElement).style.transform = `scale(${scale})`;
            }
            const applyMode = (mode: 'fit' | 'actual') => {
                if (!previewWrapper || !previewContent) return;
                if (mode === 'actual') {
                    (previewWrapper as HTMLElement).style.overflow = 'auto';
                    (previewContent as HTMLElement).style.transform = 'none';
                    toggleFitBtn.querySelector('span')!.textContent = 'Fit to view';
                    userOverride = true;
                } else {
                    (previewWrapper as HTMLElement).style.overflow = 'hidden';
                    userOverride = false;
                    requestAnimationFrame(fitToView);
                    toggleFitBtn.querySelector('span')!.textContent = 'Actual size';
                }
            };

            // Initialize preference and listeners
            const saved = (localStorage.getItem(PREF_KEY) as 'fit' | 'actual' | null) ?? 'fit';
            applyMode(saved);
            window.addEventListener('resize', () => requestAnimationFrame(fitToView));
            requestAnimationFrame(fitToView);
            toggleFitBtn.addEventListener('click', () => {
                const current = (toggleFitBtn.querySelector('span')!.textContent || '').includes('Actual') ? 'fit' : 'actual';
                const next = current === 'fit' ? 'actual' : 'fit';
                localStorage.setItem(PREF_KEY, next);
                applyMode(next);
            });
        });
    </script>